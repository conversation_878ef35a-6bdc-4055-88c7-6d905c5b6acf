using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Curio.Infrastructure.Configuration;

/// <summary>
/// 环境配置扩展方法，支持灵活的本地开发配置
/// </summary>
public static class EnvironmentConfigurationExtensions
{
    /// <summary>
    /// 添加灵活的配置源，支持本地开发配置
    /// </summary>
    public static IConfigurationBuilder AddFlexibleConfiguration(
        this IConfigurationBuilder builder,
        IHostEnvironment environment)
    {
        // 1. 基础配置文件（版本控制）
        builder.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        // 2. 环境特定配置文件（版本控制）
        builder.AddJsonFile($"appsettings.{environment.EnvironmentName}.json", optional: true, reloadOnChange: true);

        // 3. 本地配置文件（不提交到版本控制，优先级最高）
        builder.AddJsonFile("appsettings.Local.json", optional: true, reloadOnChange: true);

        // 4. 用户机密（开发环境）
        if (environment.IsDevelopment())
        {
            builder.AddUserSecrets<Program>();
        }

        // 5. 环境变量（最高优先级，用于生产环境）
        builder.AddEnvironmentVariables();

        return builder;
    }



    /// <summary>
    /// 获取当前配置的摘要信息（用于调试）
    /// </summary>
    public static string GetConfigurationSummary(this IConfiguration configuration)
    {
        var dbSettings = configuration.GetSection("Database").Get<DatabaseSettings>() ?? new DatabaseSettings();
        var kafkaSettings = configuration.GetSection("Kafka").Get<KafkaSettings>() ?? new KafkaSettings();
        var orleansSettings = configuration.GetSection("Orleans").Get<OrleansSettings>() ?? new OrleansSettings();
        var appSettings = configuration.GetSection("Application").Get<ApplicationSettings>() ?? new ApplicationSettings();

        var summary = new List<string>
        {
            "=== Configuration Summary ===",
            $"Environment: {appSettings.Environment}",
            "",
            "Database:",
            $"  Host: {dbSettings.Host}",
            $"  Port: {dbSettings.Port}",
            $"  Database: {dbSettings.Database}",
            $"  Username: {dbSettings.Username}",
            $"  Password: {(string.IsNullOrEmpty(dbSettings.Password) ? "Not Set" : "***")}",
            "",
            "Kafka:",
            $"  Brokers: {string.Join(",", kafkaSettings.BrokerList)}",
            $"  Consumer Group: {kafkaSettings.ConsumerGroupId}",
            $"  Security Protocol: {kafkaSettings.SecurityProtocol}",
            "",
            "Orleans:",
            $"  Cluster ID: {orleansSettings.ClusterId}",
            $"  Service ID: {orleansSettings.ServiceId}",
            "",
            "Security:",
            $"  JWT Secret: {(string.IsNullOrEmpty(appSettings.Security.Jwt.SecretKey) ? "Not Set" : "***")}",
            $"  Encryption Key: {(string.IsNullOrEmpty(appSettings.Security.Encryption.Key) ? "Not Set" : "***")}",
            ""
        };

        return string.Join(Environment.NewLine, summary);
    }

    /// <summary>
    /// 检查配置是否来自环境变量
    /// </summary>
    public static bool IsFromEnvironmentVariable(this IConfiguration configuration, string key)
    {
        // 检查环境变量中是否存在该键
        return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable(key));
    }

    /// <summary>
    /// 获取配置值的来源信息
    /// </summary>
    public static string GetConfigurationSource(this IConfiguration configuration, string key)
    {
        var value = configuration[key];
        if (string.IsNullOrEmpty(value))
            return "Not Set";

        if (configuration.IsFromEnvironmentVariable(key))
            return "Environment Variable";

        // 这里可以扩展检查其他来源
        return "Configuration File";
    }
}

/// <summary>
/// 配置调试助手
/// </summary>
public static class ConfigurationDebugHelper
{
    /// <summary>
    /// 打印配置摘要（仅在开发环境）
    /// </summary>
    public static void PrintConfigurationSummary(IConfiguration configuration, IHostEnvironment environment)
    {
        if (!environment.IsDevelopment()) return;

        Console.WriteLine();
        Console.WriteLine(configuration.GetConfigurationSummary());
        Console.WriteLine("=== End Configuration Summary ===");
        Console.WriteLine();
    }

    /// <summary>
    /// 检查关键配置项
    /// </summary>
    public static void ValidateKeyConfigurations(IConfiguration configuration)
    {
        var keyConfigs = new Dictionary<string, string>
        {
            ["Database Host"] = configuration["DATABASE_HOST"] ?? "localhost",
            ["Database Port"] = configuration["DATABASE_PORT"] ?? "5432",
            ["Kafka Brokers"] = configuration["KAFKA_BROKERS"] ?? "localhost:9092",
            ["Orleans Cluster"] = configuration["ORLEANS_CLUSTER_ID"] ?? "curio-cluster-dev"
        };

        Console.WriteLine("🔍 Key Configuration Check:");
        foreach (var config in keyConfigs)
        {
            var source = configuration.GetConfigurationSource(config.Key.Replace(" ", "_").ToUpper());
            Console.WriteLine($"  {config.Key}: {config.Value} (Source: {source})");
        }
        Console.WriteLine();
    }
}
