using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Orleans.Streams;
using Orleans.Streams.Kafka.Config;
using Curio.Infrastructure;
using Curio.Infrastructure.Configuration;
using Curio.Infrastructure.Services;

var builder = Host.CreateDefaultBuilder(args)
    .ConfigureServices((context, services) =>
    {
        // Register infrastructure services
        services.AddInfrastructureServices(context.Configuration);
    });

// 配置Orleans Silo
builder.UseOrleans((context, siloBuilder) =>
{
    // Get configuration settings
    var orleansSettings = context.Configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
    var kafkaSettings = context.Configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>() ?? new KafkaSettings();

    // Get connection strings using extension methods
    var clusteringConnectionString = context.Configuration.GetOrleansConnectionString("clustering");
    var storageConnectionString = context.Configuration.GetOrleansConnectionString("storage");
    var remindersConnectionString = context.Configuration.GetOrleansConnectionString("reminders");
    var kafkaBrokers = context.Configuration.GetKafkaBrokersString();

    // Debug: Print connection strings
    Console.WriteLine($"Debug - Clustering Connection: {clusteringConnectionString}");
    Console.WriteLine($"Debug - Storage Connection: {storageConnectionString}");
    Console.WriteLine($"Debug - Reminders Connection: {remindersConnectionString}");
    siloBuilder
        // 集群配置 - 使用PostgreSQL clustering
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = clusteringConnectionString;
            options.Invariant = "Npgsql";
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = orleansSettings.ClusterId;
            options.ServiceId = orleansSettings.ServiceId;
        })

        // Orleans状态存储配置
        .AddAdoNetGrainStorage("Default", options =>
        {
            options.ConnectionString = storageConnectionString;
            options.Invariant = "Npgsql";
        })
        .AddAdoNetGrainStorage("JournaledGrainState", options =>
        {
            options.ConnectionString = storageConnectionString;
            options.Invariant = "Npgsql";
        })

        // JournaledGrain Event Sourcing配置
        .AddLogStorageBasedLogConsistencyProvider("EventSourcing")

        // Kafka Streams配置
        .AddKafka("KafkaStreams")
        .WithOptions(options =>
        {
            options.BrokerList = kafkaSettings.BrokerList;
            options.ConsumerGroupId = kafkaSettings.ConsumerGroupId;

            // Topics配置
            foreach (var topic in kafkaSettings.Topics)
            {
                options.AddTopic(topic);
            }
        })
        .AddJson()
        .Build()

        // PubSub存储
        .AddMemoryGrainStorage("PubSubStore")

        // Orleans Reminders（定时任务）
        .UseAdoNetReminderService(options =>
        {
            options.ConnectionString = remindersConnectionString;
            options.Invariant = "Npgsql";
        })

        // 配置日志
        .ConfigureLogging(logging => logging.AddConsole())

        // 配置Orleans内部服务
        .ConfigureServices(services =>
        {
            // 注意：IResilientEventPublisher 不在这里注册
            // 它在每个 Grain 的 OnActivateAsync 中创建，因为需要访问 IStreamProvider
            // IStreamProvider 只在 Grain 上下文中可用，不在 DI 容器中
        });
});

var app = builder.Build();

// 启动Orleans Silo
var configuration = app.Services.GetRequiredService<IConfiguration>();
var orleansSettings = configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
var kafkaBrokers = configuration.GetKafkaBrokersString();

Console.WriteLine("🚀 Starting Curio Orleans Silo...");
Console.WriteLine($"📊 Cluster: {orleansSettings.ClusterId}");
Console.WriteLine($"🏷️  Service: {orleansSettings.ServiceId}");
Console.WriteLine($"🗄️  Database: PostgreSQL");
Console.WriteLine($"🔗 Clustering: PostgreSQL ADO.NET");
Console.WriteLine($"📨 Kafka: {kafkaBrokers}");
Console.WriteLine($"⚡ Event Sourcing: Enabled with Kafka streaming");
Console.WriteLine();

await app.RunAsync();
